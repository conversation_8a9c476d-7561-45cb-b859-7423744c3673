/**
 * 【换量任务功能】Base64编码解码工具函数
 * 使用成熟的 js-base64 库
 */

import { Base64 } from 'js-base64';

/**
 * 将字符串编码为base64
 * 使用 js-base64 库，自动处理React Native兼容性
 * @param str 要编码的字符串
 * @returns base64编码后的字符串
 */
export const encodeBase64 = (str: string): string => {
  try {
    // 使用 js-base64 库进行编码
    return Base64.encode(str);
  } catch (error) {
    console.error('Base64编码失败:', error);
    return '';
  }
};

/**
 * 将base64字符串解码
 * 使用 js-base64 库，自动处理React Native兼容性
 * @param base64Str base64编码的字符串
 * @returns 解码后的原始字符串
 */
export const decodeBase64 = (base64Str: string): string => {
  try {
    // 使用 js-base64 库进行解码
    return Base64.decode(base64Str);
  } catch (error) {
    console.error('Base64解码失败:', error);
    return '';
  }
};

/**
 * 【换量任务功能】构建完整的deeplink URL
 * 支持手动拼接 - 从外部deeplink参数重新构建iting://链接
 * @param params deeplink参数对象
 * @returns 完整的iting://链接
 */
export const buildDeeplinkUrl = (params: Record<string, any>): string => {
  const baseUrl = 'iting://open'
  const searchParams = new URLSearchParams()

  // 定义核心必需参数（换量任务必须的）
  const coreParams = [
    'msg_type',
    'bundle',
    'cid',
    'channelName',
    'token',
    'is_growth_exchange_welfare'
  ]

  // 定义扩展参数（可选的）
  const extendedParams = [
    'channelid',  // 备用渠道标识
    'task',       // 任务标识
    'url',        // 跳转链接
    'srcChannel', // 来源渠道
    'ip',         // 调试IP
    '__ip',       // 调试IP备用
    '__debug'     // 调试标识
  ]

  // 首先添加核心参数
  coreParams.forEach(key => {
    if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
      searchParams.append(key, params[key].toString())
    }
  })

  // 然后添加扩展参数
  extendedParams.forEach(key => {
    if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
      searchParams.append(key, params[key].toString())
    }
  })

  // 排除所有不应该包含在deeplink中的内部参数
  const excludeParams = new Set([
    'originalDeeplink',
    'isLogin',
    'fallbackParams',
    'initData',        // React Native内部数据
    'dispatch',        // Redux dispatch函数
    'rootTag',         // React Native内部标识
    'fragmentName',    // 页面片段名称
    'showNoLoginCheckInPop', // 内部UI状态
    'isDarkMode',      // 内部UI状态
    'withMonthlyTicket', // 内部业务状态
    'modalStatus',     // 内部模态框状态
    '__itingOrginURL', // 【修复】iOS特有的原始URL参数，会导致数据重复
    'context',         // 内部上下文数据
    'dev_host',        // 开发环境主机
    'entry',           // 入口标识
    '__kItingExtraSourceKey', // 内部额外来源标识
    'dev',             // 开发环境标识
    'platform',        // 平台标识（已包含在其他地方）
    ...coreParams,
    ...extendedParams
  ])

  // 添加其他有效参数
  Object.keys(params).forEach(key => {
    if (!excludeParams.has(key) && params[key] !== undefined && params[key] !== null && params[key] !== '') {
      // 排除函数类型和复杂对象
      const value = params[key];
      if (typeof value !== 'function' && typeof value !== 'object') {
        searchParams.append(key, value.toString())
      }
    }
  })

  return `${baseUrl}?${searchParams.toString()}`
}
