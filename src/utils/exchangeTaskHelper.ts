/**
 * 【换量任务功能】换量任务执行和上报工具函数
 *
 * 项目作用：福利中心换量任务功能 - 换量任务执行逻辑和数据上报
 *
 * 主要功能：
 * - executeExchangeTask: 执行换量任务跳转逻辑
 * - canExecuteExchangeTask: 判断是否可执行换量任务
 * - canClaimExchangeTaskReward: 判断是否可领取换量任务奖励
 * - handleExchangeTaskReturn: 处理外部APP拉起后的参数解析
 * - initExchangeTaskReturnListener: 初始化换量任务返回监听
 *
 * 注意：埋点上报已统一到DailyTaskItem组件中处理，确保每日任务和换量任务使用相同的上报字段
 */

import { Toast } from '@xmly/rn-sdk';
import { isIOS } from '@xmly/rn-utils/dist/device';
import { DailyTaskItem, ExchangeTaskStatus } from 'services/welfare/dailyTask';
import { completeExchangeTask } from 'services/welfare/exchangeTask';
import openThirdpartyApp from 'utilsV2/openThirdpartyApp';
import xmlog from 'utilsV2/xmlog';
import GlobalEventEmitter from 'utilsV2/globalEventEmitter';
import { finishExchangeTask } from '../servicesV2/finishExchangeTask';
import { FinishExchangeTaskParams } from '../typesV2/finishExchangeTaskType';
import { encodeBase64, buildDeeplinkUrl } from './base64Helper';

/**
 * 【换量任务功能】验证是否为有效的换量任务
 * 充分必要条件：
 * 1. is_growth_exchange_welfare=true
 * 2. cid、channelName、token 都有有效值（非空且不是占位符）
 */
export const isValidExchangeTask = (params: {
  is_growth_exchange_welfare?: string;
  cid?: string | number;
  channelName?: string;
  token?: string;
}): boolean => {
  const { is_growth_exchange_welfare, cid, channelName, token } = params;

  // 条件1：必须是福利中心换量任务
  if (is_growth_exchange_welfare !== 'true') {
    return false;
  }

  // 条件2：cid必须有有效值
  const cidStr = cid?.toString();
  if (!cidStr || cidStr.trim() === '' || cidStr === '__CID__') {
    return false;
  }

  // 条件3：channelName必须有有效值
  if (!channelName || channelName.trim() === '' || channelName === '__CNAME__') {
    return false;
  }

  // 条件4：token必须有有效值
  if (!token || token.trim() === '' || token === '__TOKEN__') {
    return false;
  }

  return true;
};

/**
 * 【换量任务功能】执行换量任务跳转逻辑
 * 复用积分中心的核心逻辑，但适配福利中心的接口
 */
export const executeExchangeTask = async (
  task: DailyTaskItem,
  onTaskComplete?: () => void
): Promise<void> => {
  try {
    const { taskId, h5Link = '', schemaLink = '', rta = false, title = '' } = task;

    if (!taskId) {
      Toast.info('任务信息异常');
      return;
    }

    // 如果有跳转链接，执行跳转
    if (schemaLink || h5Link) {
      await executeTaskJump({
        taskId,
        h5Link,
        schemaLink,
        title,
        rta,
        onTaskComplete,
      });
    } else {
      Toast.info('任务跳转信息缺失');
    }
  } catch (error) {
    console.error('执行换量任务失败:', error);
    Toast.info('执行任务失败～');
  }
};

/**
 * 【换量任务功能】执行任务跳转
 */
const executeTaskJump = async (params: {
  taskId: number;
  h5Link: string;
  schemaLink: string;
  title: string;
  rta: boolean;
  onTaskComplete?: () => void;
}): Promise<void> => {
  const { taskId, h5Link, schemaLink, title, rta, onTaskComplete } = params;

  try {
    // 使用复用的第三方应用打开逻辑
    await openThirdpartyApp({
      schema: schemaLink,
      h5Link,
      onFail: () => {
        Toast.info('执行任务失败～');
      },
      onSuccess: async () => {
        // 非RTA任务需要手动标记完成
        if (!rta) {
          await markTaskAsCompleted(taskId);
        }
        
        // 执行任务完成回调
        if (onTaskComplete) {
          // iOS需要在应用恢复活跃状态时刷新，Android可以立即刷新
          if (isIOS) {
            scheduleRefreshOnAppActive(onTaskComplete);
          } else {
            onTaskComplete();
          }
        }
      },
      confirmBeforeOpen: false, // 可以根据需要配置
      eventTrack: (type: '吊起 app' | '吊起下载页') => {
        // 埋点上报
        xmlog.event(49511, 'others', { 
          type, 
          from: title,
          source: 'exchangeTask' 
        });
      },
    });
  } catch (error) {
    console.error('任务跳转失败:', error);
    Toast.info('执行任务失败～');
  }
};

/**
 * 【换量任务功能】标记非RTA任务为已完成
 */
const markTaskAsCompleted = async (taskId: number): Promise<void> => {
  try {
    const result = await completeExchangeTask({ taskId });
    
    if (!result?.data?.success) {
      console.warn('标记任务完成失败:', result?.data?.message);
    }
  } catch (error) {
    console.error('标记任务完成失败:', error);
  }
};

/**
 * 【换量任务功能】在应用恢复活跃状态时执行刷新
 * 复用积分中心的调度任务逻辑
 */
const scheduleRefreshOnAppActive = (callback: () => void): void => {
  // 这里可以使用现有的 scheduleTask 逻辑
  // 或者简单的 AppState 监听
  // const handleAppStateChange = (nextAppState: string) => {
  //   if (nextAppState === 'active') {
  //     callback();
  //     // 移除监听器，避免重复执行
  //     // AppState.removeEventListener('change', handleAppStateChange);
  //   }
  // };

  // 延迟执行，给跳转一些时间
  setTimeout(() => {
    callback();
  }, 2000);
};

/**
 * 【换量任务功能】判断是否可以执行换量任务
 */
export const canExecuteExchangeTask = (task: DailyTaskItem): boolean => {
  return task.status === ExchangeTaskStatus.UNFINISHED;
};

/**
 * 【换量任务功能】判断是否可以领取换量任务奖励
 */
export const canClaimExchangeTaskReward = (task: DailyTaskItem): boolean => {
  return task.status === ExchangeTaskStatus.FINISHED;
};

/**
 * 【换量任务功能】获取换量任务的跳转确认配置
 * 复用积分中心的配置逻辑
 */
export const getExchangeTaskConfirmConfig = async (_taskId: number): Promise<boolean> => {
  // 这里可以复用 ConfigCenter 的逻辑
  // const configRes = await ConfigCenter.getConfig('toc', 'exchange_task_confirm');
  // 暂时返回 false，不需要确认
  return false;
};

/**
 * 【废弃】换量任务点击事件上报
 * @deprecated 已统一到DailyTaskItem组件的clickReport函数中，确保字段一致性
 */
export const reportExchangeTaskClick = (task: DailyTaskItem, action: string): void => {
  xmlog.click(67695, 'ExchangeTaskItem', {
    currPage: 'welfareCenter',
    moduleTitle: '每日任务',
    taskTitle: task.title,
    taskId: task.taskId?.toString() || '',
    action,
  });
};

/**
 * 【废弃】换量任务曝光事件上报
 * @deprecated 已统一到DailyTaskItem组件的onShow函数中，确保字段一致性
 */
export const reportExchangeTaskExposure = (task: DailyTaskItem, index: number): void => {
  xmlog.event(67696, 'slipPage', {
    currPage: 'welfareCenter',
    moduleTitle: '每日任务',
    taskTitle: task.title,
    taskId: task.taskId?.toString() || '',
    positionNew: (index + 1).toString(),
    taskType: 'exchange',
  });
};







/**
 * 【换量任务功能】向服务端上报换量任务完成 - 新版本
 * 使用新的专用接口：/incentive/ting/welfare/finishThirdpartyTask/exchangeTask
 *
 * 新方案特点：
 * 1. 使用专用的换量任务接口
 * 2. 将完整的deeplink进行base64编码后上报
 * 3. 只保留核心参数，减少接口复杂度
 * 4. 【重要】前端只关注响应中的code和msg，其他字段仅用于日志，不做任何toast或业务处理
 * 5. 【新增】支持登录状态字段，用于区分登录/未登录的换量任务
 */
export const reportExchangeTaskCompletion = async (params: {
  channelId: number;         // 渠道ID
  token: string;
  channelName: string;       // 渠道名称
  msgType: string;
  task: string;              // 【新增】任务标识字段
  isGrowthExchange: boolean;
  originalDeeplink: string; // 完整的原始deeplink
  isLogin?: boolean;        // 【新增】登录状态，默认为true（已登录）
  // 【新增】备用参数，用于重建deeplink
  fallbackParams?: Record<string, any>;
}): Promise<void> => {
  try {
    // 【关键功能】确保有有效的deeplink - 支持手动拼接
    let finalDeeplink = params.originalDeeplink;

    // 如果原始deeplink为空或无效，手动拼接重建
    if (!finalDeeplink || finalDeeplink.trim() === '') {
      if (params.fallbackParams) {
        // 优先使用完整的备用参数拼接
        finalDeeplink = buildDeeplinkUrl(params.fallbackParams);
      } else {
        // 从现有上报参数拼接基本的deeplink
        const reconstructedParams = {
          msg_type: params.msgType,
          bundle: 'rn_credit_center',
          cid: params.channelId.toString(),
          channelName: params.channelName,
          token: params.token,
          is_growth_exchange_welfare: params.isGrowthExchange.toString()
        };
        finalDeeplink = buildDeeplinkUrl(reconstructedParams);
      }
    }

    // 【重要】现在允许手动拼接，所以即使构建失败也继续处理
    if (!finalDeeplink || finalDeeplink.trim() === '') {
      finalDeeplink = ''; // 明确设置为空字符串
    }

    // 将完整的deeplink进行base64编码
    const deeplinkData = finalDeeplink ? encodeBase64(finalDeeplink) : '';

    // 构造新接口的上报参数
    const reportParams: FinishExchangeTaskParams = {
      channelId: params.channelId,
      token: params.token,
      channelName: params.channelName,
      msgType: params.msgType,
      task: params.task, // 【新增】任务标识字段
      isGrowthExchange: params.isGrowthExchange,
      deeplinkData: deeplinkData, // base64编码的完整deeplink
      isLogin: params.isLogin !== undefined ? params.isLogin : true, // 【新增】登录状态，默认为已登录
    };

    // 调用新的换量任务专用接口
    const result = await finishExchangeTask(reportParams);

    // 【注意】新接口只关注ret和msg，仅输出日志，不做任何toast和其他动作
    if (result?.ret === 0) {
      console.log('✅ 换量任务完成上报成功 (新接口):', {
        ret: result.ret,
        msg: result.msg,
        // 其他字段仅用于日志记录，不做业务处理
        taskId: result.data?.taskId,
        channelName: result.data?.channelName,
        reward: result.data?.reward
      });
    } else {
      console.error('❌ 换量任务完成上报失败 (新接口):', {
        ret: result?.ret,
        msg: result?.msg
      });
    }
  } catch (error) {
    console.error('❌ 换量任务完成上报异常 (新接口):', error);
  }
};

/**
 * 【换量任务功能】处理外部APP拉起后的参数解析和任务完成上报
 * 复用积分中心的处理逻辑，当外部APP拉起喜马拉雅时调用此函数
 */
export const handleExchangeTaskReturn = async (params: Record<string, any>): Promise<void> => {
  try {
    console.log('🔄 外部APP拉起参数:', params);
    console.log('🔄 参数详细信息:', JSON.stringify(params, null, 2));

    // 解析换量任务相关参数（兼容多种参数格式）
    const {
      srcChannel,
      channelName,
      channelid,        // 实际deeplink中的字段
      cid,              // 实际的渠道ID字段
      token,
      task,             // 【新增】任务标识字段
      is_growth_exchange_welfare,
      msg_type
    } = params;

    // 【换量任务判断】充分必要条件：is_growth_exchange_welfare=true 且关键参数都有有效值
    const isExchangeTask = isValidExchangeTask({
      is_growth_exchange_welfare,
      cid,
      channelName: channelName || channelid,
      token
    });

    if (isExchangeTask) {
      // 验证通过，处理换量任务

      // 【关键功能】构建完整的iting://deeplink
      // 优先使用原始deeplink，如果没有则从参数重新构建
      let finalDeeplink = params.originalDeeplink;

      if (!finalDeeplink || finalDeeplink.trim() === '') {
        // 重新构建iting://链接
        finalDeeplink = buildDeeplinkUrl(params);
      }

      // 确保deeplink不为空
      if (!finalDeeplink || finalDeeplink.trim() === '') {
        console.error('❌ 无法构建有效的deeplink，跳过上报');
        return;
      }

      // 向服务端上报任务完成 (使用新接口)
      await reportExchangeTaskCompletion({
        channelId: parseInt(cid?.toString() || '0'),
        token: token || '',
        channelName: channelName || channelid || 'unknown',
        msgType: msg_type || '94', // 外部跳转喜马拉雅福利页固定使用94
        task: task || '', // 【新增】任务标识字段，从deeplink中解析获取
        isGrowthExchange: is_growth_exchange_welfare === 'true',
        originalDeeplink: finalDeeplink, // 使用构建好的完整deeplink
        isLogin: true, // 【新增】标记为已登录状态（因为能正常返回说明已登录）
        fallbackParams: params, // 【新增】传递原始参数作为备用
      });

      // 触发任务完成事件，通知福利中心刷新任务列表
      GlobalEventEmitter.emit('exchangeTaskCompleted', {
        taskId: null, // 外部调起不需要taskId
        srcChannel: srcChannel || 'growth_exchange',
        channelName: channelName || channelid,
        channelId: cid,
        timestamp: Date.now(),
      });
    }
  } catch (error) {
    console.error('处理换量任务返回参数失败:', error);
  }
};















/**
 * 【换量任务功能】初始化换量任务返回监听
 * 在福利中心页面初始化时调用，监听外部APP拉起事件
 */
export const initExchangeTaskReturnListener = (): (() => void) => {
  const handleTaskCompleted = (data: any) => {
    console.log('🎯 换量任务完成事件:', data);

    // 这里可以触发福利中心每日任务列表刷新
    // 具体实现需要根据福利中心的状态管理方式调整
    GlobalEventEmitter.emit('refreshDailyTasks', data);
  };

  // 监听换量任务完成事件
  const listener = GlobalEventEmitter.addListener('exchangeTaskCompleted', handleTaskCompleted);

  return () => {
    listener.remove();
  };
};
