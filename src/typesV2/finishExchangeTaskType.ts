/**
 * 【换量任务功能】第三方换量任务完成上报接口参数类型
 * 新接口：/incentive/ting/welfare/finishThirdpartyTask/exchangeTask
 */
export type FinishExchangeTaskParams = {
  // 核心参数
  channelId: number          // 渠道ID
  token: string              // 任务令牌
  channelName: string        // 渠道名称
  msgType: string            // 消息类型 (如: "94")
  task: string               // 【新增】任务标识，从deeplink中解析获取
  isGrowthExchange: boolean  // 是否增长换量任务
  isLogin: boolean           // 【新增】登录状态，用于区分登录/未登录的换量任务

  // 完整deeplink信息 (base64编码)
  deeplinkData: string       // 完整的iting://链接进行base64编码后的字符串
}

/**
 * 【换量任务功能】第三方换量任务完成上报接口响应数据类型
 *
 * 注意：前端只关注 ret 和 msg 字段，其他字段仅用于日志输出
 */
export type FinishExchangeTaskResponse = {
  taskId?: string
  channelName?: string
  channelId?: number
  reward?: number
  status?: string
  reportTime?: number
  deeplinkData?: string
}
