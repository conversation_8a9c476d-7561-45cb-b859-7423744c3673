/**
 * 【换量任务功能】第三方换量任务完成上报服务
 * 新接口：/incentive/ting/welfare/finishThirdpartyTask/exchangeTask
 */

import request, { ResDataType } from './request'
import { API_ADSE } from '../constantsV2/apiConfig'
import { FinishExchangeTaskParams, FinishExchangeTaskResponse } from '../typesV2/finishExchangeTaskType'

/**
 * 【换量任务功能】上报第三方换量任务完成
 *
 * @param params 上报参数
 * @returns Promise<ResDataType<FinishExchangeTaskResponse> | undefined>
 */
export const finishExchangeTask = async (params: FinishExchangeTaskParams): Promise<ResDataType<FinishExchangeTaskResponse> | undefined> => {
  const timestamp = Date.now()

  return request<FinishExchangeTaskResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/finishThirdpartyTask/exchangeTask/ts-${timestamp}`,
    option: {
      method: 'post',
      data: JSON.stringify(params),
      headers: {
        'Content-Type': 'application/json',
      },
      catchJsonParse: true,
    },
    tip: false,
  })
}
