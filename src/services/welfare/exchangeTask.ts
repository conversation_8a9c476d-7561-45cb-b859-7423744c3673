/**
 * 【换量任务功能】换量任务服务和工具函数
 *
 * 项目作用：福利中心换量任务功能 - 换量任务API服务和业务逻辑工具
 *
 * 主要功能：
 * - completeExchangeTask: 完成换量任务API（非RTA任务完成后调用）
 * - getExchangeTaskGuide: 获取换量任务跳转参数API（备用接口）
 * - isExchangeTask: 判断是否为换量任务
 * - getExchangeTaskButtonText: 获取换量任务按钮文案
 * - isExchangeTaskButtonClickable: 判断换量任务按钮是否可点击
 * - getExchangeTaskButtonColorState: 获取换量任务按钮颜色状态
 *
 * 这些函数用于在DailyTaskItem组件中处理换量任务的显示和交互逻辑
 */

import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from '../../servicesV2/request';
import { DailyTaskItem, ExchangeTaskStatus } from './dailyTask';

// 【换量任务功能】换量任务完成接口参数
export interface CompleteExchangeTaskParams {
  taskId: number;
  aid?: number;
}

// 【换量任务功能】换量任务完成接口响应
export interface CompleteExchangeTaskResult {
  success: boolean;
  code: number;
  message?: string;
}

// 【换量任务功能】换量任务跳转参数获取接口参数
export interface GetExchangeTaskGuideParams {
  taskId: number;
  aid?: number;
}

// 【换量任务功能】换量任务跳转参数响应
export interface ExchangeTaskGuideResult {
  success: boolean;
  code: number;
  h5Link?: string;
  schemaLink?: string;
  taskId: number;
}

/**
 * 【换量任务功能】完成换量任务 - 标记非RTA任务为已完成
 * 对应原来的 openapi-feeds-stream-app/activity/complete/task 接口
 */
export const completeExchangeTask = async (
  params: CompleteExchangeTaskParams
): Promise<ResDataType<CompleteExchangeTaskResult>> => {
  const { taskId, aid } = params;
  
  return request<CompleteExchangeTaskResult>({
    ...API_ADSE,
    url: `incentive/ting/welfare/completeExchangeTask/ts-${Date.now()}`,
    option: {
      method: 'post',
      data: JSON.stringify({
        taskId,
        aid: aid || 0,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    },
  });
};

/**
 * 【换量任务功能】获取换量任务跳转参数
 * 对应原来的 web-activity/task/v2/genGuideLink 接口
 * 注意：新版本中这个功能已经集成到每日任务列表接口中
 */
export const getExchangeTaskGuide = async (
  params: GetExchangeTaskGuideParams
): Promise<ResDataType<ExchangeTaskGuideResult>> => {
  const { taskId, aid } = params;
  
  return request<ExchangeTaskGuideResult>({
    ...API_ADSE,
    url: `incentive/ting/welfare/getExchangeTaskGuide/ts-${Date.now()}`,
    option: {
      method: 'get',
      data: {
        taskId,
        aid: aid || 0,
      },
    },
  });
};

/**
 * 【换量任务功能】判断任务是否为换量任务
 * 使用type字段识别，与原始每日任务数据结构保持一致
 */
export const isExchangeTask = (task: DailyTaskItem): boolean => {
  return task.type?.toString() === 'EXCHANGE';
};

/**
 * 【换量任务功能】获取换量任务按钮文案
 */
export const getExchangeTaskButtonText = (task: DailyTaskItem): string => {
  if (!isExchangeTask(task)) {
    return task.btnText || '去领取';
  }

  const { status, contextMap } = task;
  
  if (contextMap) {
    return contextMap[status?.toString() || '0'] || '去完成';
  }

  // 默认文案映射
  switch (status) {
    case ExchangeTaskStatus.UNFINISHED:
      return '去完成';
    case ExchangeTaskStatus.FINISHED:
      return '领取';
    case ExchangeTaskStatus.CLAIMED:
      return '已领取';
    default:
      return '去完成';
  }
};

/**
 * 【换量任务功能】获取换量任务按钮是否可点击
 */
export const isExchangeTaskButtonClickable = (task: DailyTaskItem): boolean => {
  if (!isExchangeTask(task)) {
    return true;
  }

  return task.status !== ExchangeTaskStatus.CLAIMED;
};

/**
 * 【换量任务功能】获取换量任务按钮颜色状态
 */
export const getExchangeTaskButtonColorState = (task: DailyTaskItem): 'primary' | 'success' | 'disabled' => {
  if (!isExchangeTask(task)) {
    return 'primary';
  }

  switch (task.status) {
    case ExchangeTaskStatus.UNFINISHED:
      return 'primary';
    case ExchangeTaskStatus.FINISHED:
      return 'success';
    case ExchangeTaskStatus.CLAIMED:
      return 'disabled';
    default:
      return 'primary';
  }
};
