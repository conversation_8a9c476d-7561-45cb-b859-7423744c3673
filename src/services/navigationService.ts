import { NavigationContainerRef } from '@react-navigation/native';
import { RootStackParamList } from '../router/type';

class NavigationService {
  private navigationRef: React.RefObject<NavigationContainerRef> | null = null;
  private originalInitialRouteName: keyof RootStackParamList = 'Home';

  setNavigationRef(ref: React.RefObject<NavigationContainerRef>) {
    this.navigationRef = ref;
  }

  setOriginalInitialRouteName(routeName: keyof RootStackParamList) {
    this.originalInitialRouteName = routeName;
  }

  // 自定义的从喝水页面返回逻辑
  goBackFromDrinkWater() {
    if (this.navigationRef?.current) {
      try {
        this.navigationRef.current.reset({
          index: 0,
          routes: [{ name: this.originalInitialRouteName }],
        });
      } catch (error) {
      }
    }
  }
}

export default new NavigationService();
