import { atom } from 'jotai';
import { ModalItem } from './types';

export const modalQueueAtom = atom<ModalItem[]>([]);
export const currentModalAtom = atom<ModalItem | null>(null);
// export const isCheckingAsyncAtom = atom<boolean>(false);

// 处理队列，决定当前弹窗
export const processModalQueueAtom = atom(
    null,
    async (get, set) => {
      console.log('------- processModalQueueAtom -------->');
      // if (get(isCheckingAsyncAtom)) return; // 正在检查异步弹窗，直接返回
  
      const queue = get(modalQueueAtom);
      if (!queue.length) {
        set(currentModalAtom, null);
        console.log('------- processModalQueueAtom --------> queue is empty');
        return;
      }
  
      const modal = queue[0];
      console.log('-------current process modal -------->', modal.id);
      console.log('------- registerModalAtom -------->processModalQueue', queue.map(item => item.id));

      // 新增 onCheck 判断，优先级最高
      if (modal.onCheck) {
        if (!modal.enable) {
          console.log('------- modal not enable -------->', modal.id);
          set(modalQueueAtom, q => q.slice(1));
          setTimeout(() => set(processModalQueueAtom), 0);
          return;
        }
        let shouldShow;
        try {
          shouldShow = modal.onCheck();
          if (shouldShow instanceof Promise) {
            shouldShow = await shouldShow;
          }
        } catch (e) {
          console.error('onCheck error:', e);
          shouldShow = false;
        }
        if (!shouldShow) {
          // 不展示，移除队首，递归处理下一个
          set(modalQueueAtom, q => q.slice(1));
          setTimeout(() => set(processModalQueueAtom), 0);
          return;
        }
      }
      
      set(currentModalAtom, modal);
      
    }
  );

/**
 * 添加弹窗
 */
export const registerModalAtom = atom(
    null,
    (get, set, modal: ModalItem) => {
      const queue = get(modalQueueAtom);
      // 去重
      const filtered = queue.filter(item => item.id !== modal.id);
      // 按优先级插入
      const newQueue = [...filtered, modal].sort((a, b) => (a.priority || 0) - (b.priority || 0));
      set(modalQueueAtom, newQueue);
      // 这里不触发处理，因为需要上一个弹窗处理完再触发
      // set(processModalQueueAtom); // 触发处理
      if (queue.length === 0) {
        set(processModalQueueAtom);
      }
      console.log('------- registerModalAtom -------->', queue.map(item => item.id), newQueue.map(item => item.id));
    }
  );

  /**
   * 批量添加弹窗
   */
  export const registerModalsAtom = atom(
    null,
    (get, set, modals: ModalItem[]) => {
      // const queue = get(modalQueueAtom);
      // console.log('------- registerModalAtom -------->queue', queue.map(item => item.id));
      // 先用新modals的id生成Set
      // const newIds = new Set(modals.map(modal => modal.id));
      // 移除队列中与新modals重复id的item
      // const filteredQueue = queue.filter(item => !newIds.has(item.id));
      // 合并并按优先级排序（新item优先）
      if (modals.length === 0) {
        set(modalQueueAtom, []);
        return;
      }
      const newQueue = [...modals].sort((a, b) => (a.priority || 0) - (b.priority || 0));
      set(modalQueueAtom, newQueue);

      console.log('------- registerModalsAtom -------->', newQueue.map(item => item.id));

      set(processModalQueueAtom);
    }
  );

/**
 * 关闭弹窗
 */
export const closeModalAtom = atom(
  null,
  (get, set, id: string, show: boolean, tag?: string) => {
    const queue = get(modalQueueAtom);
    const current = get(currentModalAtom);

    console.log('------- closeModalAtom --------> ', id, current?.id, tag, show);

    // 如果传了 id，且当前弹窗 id 不等于传入 id，则不关闭
    if (id && current?.id !== id) {
      return;
    }

    // 只移除队首（当前弹窗）
    set(modalQueueAtom, queue.slice(1));
    // console.log('------- registerModalAtom -------->closeModalQueue', get(modalQueueAtom).map(item => item.id));

    set(processModalQueueAtom);
  }
);