import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {useEffect } from "react";
import { coinsModalShowAtom, modalShowDateAtom, coinsModalExposeAtom } from "../store";
import dayjs from "dayjs";
import {shareConinsTaskAtom} from "../../ShareCoins/store";
import xmlog from "utilsV2/xmlog";
import { AreaType, ActionType } from "../utils";
import { closeModalAtom } from "components/common/ModalManager/store";

export default function useShowCashModal() {
  const [showDate, setShowDate] = useAtom(modalShowDateAtom);
  const today = dayjs().format('__YYYY-MM-DD__');
  const taskInfo = useAtomValue(shareConinsTaskAtom)
  // const signInModalShow = useAtomValue(signInModalShowAtom);
  const setShowModal = useSetAtom(coinsModalShowAtom);
  const showModal = useAtomValue(coinsModalShowAtom);
  const [coinsModalExpose, setCoinsModalExpose] = useAtom(coinsModalExposeAtom);
  const onCloseModal = useSetAtom(closeModalAtom);

  useEffect(() => {
    const modalInfo = taskInfo?.shareCoinsModalInfo

    // const nativeInfo = nativeInfoModule.getInfo()
    //console.log('------- useShowCashModal -------->', modalInfo, showDate, today, taskInfo.alreadyTimes, taskInfo.totalTimes);

    if (!taskInfo || taskInfo.loading) {
      //console.log('------- useShowCashModal loading -------->', taskInfo?.loading, modalInfo, taskInfo);
      return;
    }

    if (modalInfo && (showDate != today || taskInfo.alreadyTimes != taskInfo.totalTimes) && taskInfo.totalTimes > 0) {
      if (taskInfo.alreadyTimes === taskInfo.totalTimes) {
        setShowDate(today);
        onCloseModal('ShareCoinsModal', false, '1');
      } else {
        setShowDate(undefined);
        setShowModal(true);
        if (!coinsModalExpose) {
          // 任务中心-福利中心-签到弹窗  控件曝光
          xmlog.event(68675, 'dialogView', { currPage: '福利中心-瓜分弹窗' });
          setCoinsModalExpose(true);
        }
      }
        
    } else {
      if (showModal === undefined ) {
        // 为了让瓜分弹窗能够正常展示，需要设置为 false
        setShowModal(false);
        onCloseModal('ShareCoinsModal', false, '2');
      }
    }
  }, [setShowModal, showDate, today, taskInfo])

  function close() {
    setShowModal(false);
    onCloseModal('ShareCoinsModal', true, '3');
    xmlog.click(68676, '', { currPage: '福利中心-瓜分弹窗', area: AreaType.Close, action: ActionType.Close });
  }

  return { close };
}